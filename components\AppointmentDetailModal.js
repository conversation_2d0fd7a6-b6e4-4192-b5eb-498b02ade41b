'use client';

import { useEffect } from 'react';

export default function AppointmentDetailModal({ appointment, isOpen, onClose }) {
  // Close modal on Escape key press
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !appointment) return null;

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format creation date
  const formatDateTime = (dateTimeString) => {
    const date = new Date(dateTimeString);
    return date.toLocaleString('it-IT', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status display info
  const getStatusInfo = (status) => {
    switch (status) {
      case 'confirmed':
        return { label: 'Confermato', color: 'bg-green-100 text-green-800' };
      case 'completed':
        return { label: 'Completato', color: 'bg-blue-100 text-blue-800' };
      case 'cancelled':
        return { label: 'Annullato', color: 'bg-red-100 text-red-800' };
      case 'no_show':
        return { label: 'Non presentato', color: 'bg-gray-100 text-gray-800' };
      default:
        return { label: status, color: 'bg-gray-100 text-gray-800' };
    }
  };

  const statusInfo = getStatusInfo(appointment.status);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-[var(--secondary-blue)] text-white p-6 rounded-t-lg">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold">Dettagli Appuntamento</h2>
              <p className="text-blue-200 mt-1">ID: {appointment.id}</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-300 text-2xl font-bold"
              aria-label="Chiudi"
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Badge */}
          <div className="flex justify-center">
            <span className={`inline-flex px-4 py-2 rounded-full text-sm font-semibold ${statusInfo.color}`}>
              {statusInfo.label}
            </span>
          </div>

          {/* Client Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-3 flex items-center">
              <span className="mr-2">👤</span>
              Informazioni Cliente
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Nome Completo</p>
                <p className="font-medium text-[var(--primary-text)]">
                  {appointment.nome} {appointment.cognome}
                </p>
              </div>
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Email</p>
                <p className="font-medium text-[var(--primary-text)]">{appointment.email}</p>
              </div>
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Telefono</p>
                <p className="font-medium text-[var(--primary-text)]">{appointment.telefono}</p>
              </div>
            </div>
          </div>

          {/* Appointment Information */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-3 flex items-center">
              <span className="mr-2">📅</span>
              Dettagli Appuntamento
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Data</p>
                <p className="font-medium text-[var(--primary-text)]">
                  {formatDate(appointment.dataAppuntamento)}
                </p>
              </div>
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Orario</p>
                <p className="font-medium text-[var(--primary-text)] text-lg">
                  {appointment.orario}
                </p>
              </div>
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Servizio</p>
                <p className="font-medium text-[var(--primary-text)]">{appointment.servizio}</p>
              </div>
              {appointment.prestazione && (
                <div>
                  <p className="text-sm text-[var(--secondary-text)]">Prestazione</p>
                  <p className="font-medium text-[var(--primary-text)]">{appointment.prestazione}</p>
                </div>
              )}
              {appointment.operatore && appointment.operatore !== 'Qualsiasi' && (
                <div>
                  <p className="text-sm text-[var(--secondary-text)]">Operatore Richiesto</p>
                  <p className="font-medium text-[var(--primary-text)]">{appointment.operatore}</p>
                </div>
              )}
            </div>
          </div>

          {/* Additional Notes */}
          {appointment.noteAggiuntive && (
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-3 flex items-center">
                <span className="mr-2">📝</span>
                Note Aggiuntive
              </h3>
              <p className="text-[var(--primary-text)] whitespace-pre-wrap">
                {appointment.noteAggiuntive}
              </p>
            </div>
          )}

          {/* System Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-3 flex items-center">
              <span className="mr-2">ℹ️</span>
              Informazioni Sistema
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-[var(--secondary-text)]">Data Creazione</p>
                <p className="font-medium text-[var(--primary-text)]">
                  {formatDateTime(appointment.createdAt)}
                </p>
              </div>
              {appointment.updatedAt && appointment.updatedAt !== appointment.createdAt && (
                <div>
                  <p className="text-sm text-[var(--secondary-text)]">Ultima Modifica</p>
                  <p className="font-medium text-[var(--primary-text)]">
                    {formatDateTime(appointment.updatedAt)}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-lg">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="bg-[var(--primary-red)] text-white px-6 py-2 rounded-lg hover:bg-[var(--hover-accent)] transition-colors"
            >
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
