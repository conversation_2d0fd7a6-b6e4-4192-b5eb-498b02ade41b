import {
  supabase,
  supabaseAdmin,
  TABLES,
  APPOINTMENT_STATUS,
  handleSupabaseError,
  convertSupabaseToJsonFormat,
  validateAppointmentData
} from './supabase.js';

/**
 * Read all appointments from Supabase
 * @returns {Array} Array of appointments in JSON format
 */
export const readAppointments = async () => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'read appointments');
    }

    // Convert to JSON format for API compatibility
    return data.map(convertSupabaseToJsonFormat);
  } catch (error) {
    console.error('Error reading appointments:', error);
    return [];
  }
};

/**
 * Add a new appointment to Supabase
 * @param {Object} appointmentData - Appointment data
 * @returns {Object} The saved appointment with ID in JSON format
 */
export const addAppointment = async (appointmentData) => {
  try {
    // Validate appointment data
    validateAppointmentData(appointmentData);

    // Check if time slot is available
    const isAvailable = await isTimeSlotAvailable(
      appointmentData.dataAppuntamento, 
      appointmentData.orario
    );

    if (!isAvailable) {
      throw new Error('TIME_SLOT_UNAVAILABLE');
    }

    // Prepare data for Supabase (convert field names)
    const supabaseData = {
      nome: appointmentData.nome,
      cognome: appointmentData.cognome,
      telefono: appointmentData.telefono && appointmentData.telefono.trim() !== ''
        ? appointmentData.telefono
        : 'Non Inserito',
      email: appointmentData.email && appointmentData.email.trim() !== ''
        ? appointmentData.email
        : 'Non Inserito',
      servizio: appointmentData.servizio,
      prestazione: appointmentData.prestazione || null,
      operatore: appointmentData.operatore || 'Qualsiasi',
      note_aggiuntive: appointmentData.noteAggiuntive || null,
      data_appuntamento: appointmentData.dataAppuntamento,
      orario: appointmentData.orario,
      status: APPOINTMENT_STATUS.CONFIRMED
    };

    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .insert([supabaseData])
      .select()
      .single();

    if (error) {
      if (error.code === '23505') {
        throw new Error('TIME_SLOT_UNAVAILABLE');
      }
      handleSupabaseError(error, 'add appointment');
    }

    // Convert back to JSON format
    return convertSupabaseToJsonFormat(data);
  } catch (error) {
    console.error('Error adding appointment:', error);
    throw error;
  }
};

/**
 * Update appointment status
 * @param {string} appointmentId - Appointment ID
 * @param {string} status - New status
 * @returns {boolean} Success status
 */
export const updateAppointmentStatus = async (appointmentId, status) => {
  try {
    if (!Object.values(APPOINTMENT_STATUS).includes(status)) {
      throw new Error('Invalid status');
    }

    // Convert appointmentId to number, handle both string and number inputs
    const numericId = typeof appointmentId === 'string' ? parseInt(appointmentId, 10) : appointmentId;

    if (isNaN(numericId)) {
      throw new Error('Invalid appointment ID');
    }

    console.log(`Updating appointment ${numericId} to status ${status}`);

    // Use admin client for update operations (bypasses RLS)
    const { data, error } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .update({ status })
      .eq('id', numericId)
      .select();

    if (error) {
      console.error('Supabase error during update appointment status:', error);
      handleSupabaseError(error, 'update appointment status');
    }

    console.log('Update result:', data);

    // Check if any rows were updated
    if (!data || data.length === 0) {
      throw new Error('No appointment found with the given ID');
    }

    return true;
  } catch (error) {
    console.error('Error updating appointment status:', error);
    return false;
  }
};

/**
 * Delete an appointment
 * @param {string} appointmentId - Appointment ID
 * @returns {boolean} Success status
 */
export const deleteAppointment = async (appointmentId) => {
  try {
    // Convert appointmentId to number, handle both string and number inputs
    const numericId = typeof appointmentId === 'string' ? parseInt(appointmentId, 10) : appointmentId;

    if (isNaN(numericId)) {
      throw new Error('Invalid appointment ID');
    }

    console.log(`Deleting appointment ${numericId}`);

    // Use admin client for delete operations (bypasses RLS)
    const { data, error } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .delete()
      .eq('id', numericId)
      .select();

    if (error) {
      console.error('Supabase error during delete appointment:', error);
      handleSupabaseError(error, 'delete appointment');
    }

    console.log('Delete result:', data);

    // Check if any rows were deleted
    if (!data || data.length === 0) {
      throw new Error('No appointment found with the given ID');
    }

    return true;
  } catch (error) {
    console.error('Error deleting appointment:', error);
    return false;
  }
};

/**
 * Get appointments by date range
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Array} Array of appointments in JSON format
 */
export const getAppointmentsByDateRange = async (startDate, endDate) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .gte('data_appuntamento', startDate)
      .lte('data_appuntamento', endDate)
      .order('data_appuntamento', { ascending: true })
      .order('orario', { ascending: true });

    if (error) {
      handleSupabaseError(error, 'get appointments by date range');
    }

    // Convert to JSON format for API compatibility
    return data.map(convertSupabaseToJsonFormat);
  } catch (error) {
    console.error('Error getting appointments by date range:', error);
    return [];
  }
};

/**
 * Check if a time slot is available
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} time - Time (HH:MM)
 * @returns {boolean} True if available, false if booked
 */
export const isTimeSlotAvailable = async (date, time) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .eq('data_appuntamento', date)
      .eq('orario', time)
      .neq('status', APPOINTMENT_STATUS.CANCELLED)
      .limit(1);

    if (error) {
      handleSupabaseError(error, 'check time slot availability');
    }

    return data.length === 0;
  } catch (error) {
    console.error('Error checking time slot availability:', error);
    return false;
  }
};

/**
 * Get appointments for a specific date
 * @param {string} date - Date (YYYY-MM-DD)
 * @returns {Array} Array of appointments in JSON format
 */
export const getAppointmentsByDate = async (date) => {
  return await getAppointmentsByDateRange(date, date);
};

/**
 * Get dashboard statistics
 * @returns {Object} Dashboard stats
 */
export const getDashboardStats = async () => {
  try {
    // Use the database function for better performance
    const { data, error } = await supabase
      .rpc('get_dashboard_stats');

    if (error) {
      handleSupabaseError(error, 'get dashboard stats');
    }

    return data || {
      total: 0,
      today: 0,
      thisWeek: 0,
      thisMonth: 0,
      byService: {},
      byStatus: {},
      recent: []
    };
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    
    // Fallback to manual calculation if the function fails
    try {
      const appointments = await readAppointments();
      const today = new Date().toISOString().split('T')[0];
      const thisWeek = getWeekRange(new Date());
      const thisMonth = getMonthRange(new Date());

      return {
        total: appointments.length,
        today: appointments.filter(app => app.dataAppuntamento === today).length,
        thisWeek: appointments.filter(app =>
          app.dataAppuntamento >= thisWeek.start &&
          app.dataAppuntamento <= thisWeek.end
        ).length,
        thisMonth: appointments.filter(app =>
          app.dataAppuntamento >= thisMonth.start &&
          app.dataAppuntamento <= thisMonth.end
        ).length,
        byService: getAppointmentsByService(appointments),
        byStatus: getAppointmentsByStatus(appointments),
        recent: appointments
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 5)
      };
    } catch (fallbackError) {
      console.error('Fallback stats calculation failed:', fallbackError);
      return {
        total: 0,
        today: 0,
        thisWeek: 0,
        thisMonth: 0,
        byService: {},
        byStatus: {},
        recent: []
      };
    }
  }
};

/**
 * Helper function to get appointments grouped by service
 * @param {Array} appointments - Array of appointments
 * @returns {Object} Appointments grouped by service
 */
const getAppointmentsByService = (appointments) => {
  return appointments.reduce((acc, appointment) => {
    const service = appointment.servizio;
    acc[service] = (acc[service] || 0) + 1;
    return acc;
  }, {});
};

/**
 * Helper function to get appointments grouped by status
 * @param {Array} appointments - Array of appointments
 * @returns {Object} Appointments grouped by status
 */
const getAppointmentsByStatus = (appointments) => {
  return appointments.reduce((acc, appointment) => {
    const status = appointment.status || 'confirmed';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});
};

/**
 * Helper function to get week range for a given date
 * @param {Date} date - Reference date
 * @returns {Object} Week start and end dates
 */
const getWeekRange = (date) => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday start
  start.setDate(diff);

  const end = new Date(start);
  end.setDate(start.getDate() + 6);

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  };
};

/**
 * Helper function to get month range for a given date
 * @param {Date} date - Reference date
 * @returns {Object} Month start and end dates
 */
const getMonthRange = (date) => {
  const start = new Date(date.getFullYear(), date.getMonth(), 1);
  const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  };
};
