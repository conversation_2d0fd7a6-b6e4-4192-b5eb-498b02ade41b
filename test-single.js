/**
 * Test singolo per verificare l'API
 */

const testData = {
  nome: "Test",
  cognome: "User",
  telefono: "",
  email: "",
  servizio: "Servizi CAF",
  prestazione: "",
  operatore: "Qualsiasi",
  noteAggiuntive: "",
  dataAppuntamento: "2025-01-13",
  orario: "10:00"
};

async function testAPI() {
  console.log('🧪 Test singolo...');
  console.log('Dati inviati:', JSON.stringify(testData, null, 2));
  
  try {
    const response = await fetch('http://localhost:3000/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    const result = await response.json();
    
    console.log('Status:', response.status);
    console.log('Risposta:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Test PASSATO');
    } else {
      console.log('❌ Test FALLITO');
    }
    
  } catch (error) {
    console.log('❌ Errore di rete:', error.message);
  }
}

testAPI().catch(console.error);
